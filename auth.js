const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const path = require('path');

const dbPath = path.join(__dirname, 'recipes.db');

// Initialize users table
function initializeUsersTable() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath);

    db.serialize(() => {
      // Create users table
      db.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`, (err) => {
        if (err) {
          db.close();
          reject(err);
          return;
        }

        // Check if default user exists
        db.get("SELECT COUNT(*) as count FROM users", (err, row) => {
          if (err) {
            db.close();
            reject(err);
            return;
          }

          if (row.count === 0) {
            // Create default user: admin/admin123
            createDefaultUser(db).then(() => {
              db.close();
              resolve();
            }).catch((error) => {
              db.close();
              reject(error);
            });
          } else {
            db.close();
            resolve();
          }
        });
      });
    });
  });
}

// Create default user
async function createDefaultUser(db) {
  return new Promise(async (resolve, reject) => {
    try {
      const hashedPassword = await bcrypt.hash('admin123', 10);

      db.run(
        "INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)",
        ['admin', '<EMAIL>', hashedPassword],
        function(err) {
          if (err) {
            reject(err);
          } else {
            console.log('✅ Default user created: admin/admin123');
            resolve();
          }
        }
      );
    } catch (error) {
      reject(error);
    }
  });
}

// Authenticate user
function authenticateUser(username, password) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath);
    
    db.get(
      "SELECT * FROM users WHERE username = ? OR email = ?",
      [username, username],
      async (err, user) => {
        if (err) {
          db.close();
          reject(err);
          return;
        }
        
        if (!user) {
          db.close();
          resolve(null); // User not found
          return;
        }
        
        try {
          const isValid = await bcrypt.compare(password, user.password_hash);
          db.close();
          
          if (isValid) {
            // Return user without password hash
            const { password_hash, ...userWithoutPassword } = user;
            resolve(userWithoutPassword);
          } else {
            resolve(null); // Invalid password
          }
        } catch (error) {
          db.close();
          reject(error);
        }
      }
    );
  });
}

// Validate password strength
function validatePassword(password) {
  const errors = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

// Validate username
function validateUsername(username) {
  const errors = [];

  if (!username || username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }

  if (username.length > 20) {
    errors.push('Username must be no more than 20 characters long');
  }

  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('Username can only contain letters, numbers, and underscores');
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

// Validate email
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return {
    isValid: emailRegex.test(email),
    errors: emailRegex.test(email) ? [] : ['Please enter a valid email address']
  };
}

// Check if username exists
function checkUsernameExists(username) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath);

    db.get(
      "SELECT id FROM users WHERE username = ?",
      [username],
      (err, row) => {
        db.close();

        if (err) {
          reject(err);
        } else {
          resolve(!!row);
        }
      }
    );
  });
}

// Check if email exists
function checkEmailExists(email) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath);

    db.get(
      "SELECT id FROM users WHERE email = ?",
      [email],
      (err, row) => {
        db.close();

        if (err) {
          reject(err);
        } else {
          resolve(!!row);
        }
      }
    );
  });
}

// Create new user with comprehensive validation
function createUser(username, email, password) {
  return new Promise(async (resolve, reject) => {
    try {
      // Validate input
      const usernameValidation = validateUsername(username);
      const emailValidation = validateEmail(email);
      const passwordValidation = validatePassword(password);

      const allErrors = [
        ...usernameValidation.errors,
        ...emailValidation.errors,
        ...passwordValidation.errors
      ];

      if (allErrors.length > 0) {
        reject(new Error(allErrors.join('. ')));
        return;
      }

      // Check if username or email already exists
      const [usernameExists, emailExists] = await Promise.all([
        checkUsernameExists(username),
        checkEmailExists(email)
      ]);

      if (usernameExists) {
        reject(new Error('Username is already taken'));
        return;
      }

      if (emailExists) {
        reject(new Error('Email address is already registered'));
        return;
      }

      // Create user
      const db = new sqlite3.Database(dbPath);
      const hashedPassword = await bcrypt.hash(password, 12); // Increased salt rounds for better security

      db.run(
        "INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)",
        [username.toLowerCase(), email.toLowerCase(), hashedPassword],
        function(err) {
          db.close();

          if (err) {
            reject(new Error('Failed to create user account'));
          } else {
            resolve({
              id: this.lastID,
              username: username.toLowerCase(),
              email: email.toLowerCase()
            });
          }
        }
      );
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = {
  initializeUsersTable,
  authenticateUser,
  createUser,
  validatePassword,
  validateUsername,
  validateEmail,
  checkUsernameExists,
  checkEmailExists
};
